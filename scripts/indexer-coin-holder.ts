/* eslint-disable @typescript-eslint/no-unused-vars */
import * as dotenv from 'dotenv';
dotenv.config();
import { NestFactory } from '@nestjs/core';
import { Injectable, Module } from '@nestjs/common';
import { SharedModule } from '@modules/shared/shared.module';
import { sleep } from '@shares/utils/common.utils';
import { TreasuryModule } from '@modules/treasury/treasury.module';
import { CoinRepository } from '@modules/coin/repositories/coin.repository';
import { Coin } from '@modules/coin/schemas/coin.schema';
import { CoinModule } from '@modules/coin/coin.module';
import { HolderModule } from '@modules/holder/holder.module';
import axios from 'axios';
import { HolderRepository } from '@modules/holder/repositories/holder.repository';
import { Decimal128 } from 'bson';
import { formatLaunchpadTokenAmount } from '@shares/utils/evm-utils';

const MAX_HOLDERS = 100;
const HYPERSCAN_API_URL = 'https://www.hyperscan.com/api/v2';
const API_RATE_LIMIT_DELAY = 250;

interface TopHolderData {
  address: string;
  balance: string;
}

@Injectable()
export class IndexerTotalHolder {
  constructor(
    private readonly coinRepository: CoinRepository,
    private readonly holderRepository: HolderRepository,
  ) {}

  async run() {
    while (true) {
      const coins = await this.coinRepository.model
        .find({})
        .sort({ _id: 1 })
        .limit(1000);
      for (const coin of coins) {
        await this.processCoin(coin);
        await sleep(200);
      }

      console.log('Sleep 30 seconds to next tick');
      await sleep(30000);
    }
  }

  private async processCoin(coin: Coin) {
    try {
      console.log(`Processing coin: ${coin.symbol} (${coin.tokenAddress})`);

      await this.fetchAndSaveTopHolders(coin.tokenAddress);
      await this.updateTotalHolders(coin.tokenAddress);
    } catch (error: any) {
      console.error(
        `Error processing coin: ${coin.tokenAddress}`,
        error?.message,
      );
    }
  }

  /**
   * Fetch and save top 100 holders
   */
  async fetchAndSaveTopHolders(tokenAddress: string): Promise<void> {
    try {
      console.log(`Fetching top holders for token: ${tokenAddress}`);

      let response: any;
      try {
        response = await axios.get(
          `${HYPERSCAN_API_URL}/tokens/${tokenAddress}/holders`,
        );
        await sleep(API_RATE_LIMIT_DELAY);
      } catch (error: any) {
        console.error(
          `Error fetching initial holders for token ${tokenAddress}:`,
          error?.message,
        );
        return;
      }

      const initHoldersData = response.data;
      const holders: TopHolderData[] = [];

      if (initHoldersData.items) {
        holders.push(
          ...initHoldersData.items.map((holder: any) => ({
            address: holder.address.hash,
            balance: formatLaunchpadTokenAmount(holder.value),
          })),
        );
      }

      let nextPageParams = initHoldersData.next_page_params;
      while (holders.length < MAX_HOLDERS && nextPageParams) {
        try {
          const response = await axios.get(
            `${HYPERSCAN_API_URL}/tokens/${tokenAddress}/holders`,
            {
              params: nextPageParams,
            },
          );
          await sleep(API_RATE_LIMIT_DELAY);

          const remainingSlots = MAX_HOLDERS - holders.length;
          const newHolders = response.data.items
            .slice(0, remainingSlots)
            .map((holder: any) => ({
              address: holder.address.hash,
              balance: formatLaunchpadTokenAmount(holder.value),
            }));

          holders.push(...newHolders);
          nextPageParams = response.data.next_page_params;
        } catch (error: any) {
          console.error(
            `Error fetching paginated holders for token ${tokenAddress}:`,
            error?.message,
          );
          return;
        }
      }

      const topHolders = holders.slice(0, MAX_HOLDERS);

      try {
        await this.saveTopHolders(tokenAddress, topHolders);
      } catch (error: any) {
        console.error(
          `Error saving holders for token ${tokenAddress}:`,
          error?.message,
        );
        return;
      }

      console.log(
        `Saved top ${topHolders.length} holders for token ${tokenAddress}`,
      );
    } catch (error: any) {
      console.error(
        `Unexpected error processing token ${tokenAddress}:`,
        error?.message,
      );
      // Don't throw - just log and continue with next token
    }
  }

  private async updateTotalHolders(tokenAddress: string): Promise<void> {
    const response = await axios.get(
      `${HYPERSCAN_API_URL}/tokens/${tokenAddress}/counters`,
    );

    const totalHolder = response.data.token_holders_count;

    await this.coinRepository.updateOne(
      { tokenAddress },
      {
        $set: {
          totalHolder,
        },
      },
    );
  }

  private async saveTopHolders(
    tokenAddress: string,
    holders: TopHolderData[],
  ): Promise<void> {
    await this.holderRepository.model.deleteMany({ tokenAddress });

    const holderDocs = holders.map((holder) => ({
      tokenAddress,
      userAddress: holder.address,
      amount: Decimal128.fromString(holder.balance),
      lastSyncedAt: Date.now(),
    }));

    if (holderDocs.length > 0) {
      await this.holderRepository.model.insertMany(holderDocs);
      console.log(
        `Saved ${holderDocs.length} top holders for token ${tokenAddress}`,
      );
    }
  }
}

@Module({
  imports: [SharedModule, TreasuryModule, CoinModule, HolderModule],
  providers: [IndexerTotalHolder],
  controllers: [],
})
class AppModule {}

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  await app.init();
  const indexer = app.get(IndexerTotalHolder);
  await indexer.run();
}

bootstrap().catch((error) => {
  console.error(error);
  process.exit(1);
});
