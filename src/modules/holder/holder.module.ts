import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Holder, HolderSchema } from './schemas/holder.schema';
import { HolderRepository } from './repositories/holder.repository';
import { CoinModule } from '@modules/coin/coin.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Holder.name, schema: HolderSchema }]),
    CoinModule,
  ],
  providers: [HolderRepository],
  exports: [HolderRepository],
})
export class HolderModule {}
